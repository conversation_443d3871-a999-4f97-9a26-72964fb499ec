"""
阶段20：时长控制 (Duration Control)
根据目标时长，对精剪后的视觉剧本进行迭代式压缩。
"""

import json
import re
from typing import Any, Dict, List, Optional

from config.prompts import BEAT_TRIM_PROMPT
from config.schemas import ModelName, TrimmedBeatResponse
from stages.base import BaseStage
from utils.ai_utils import secondary_client


class Stage20DurationControl(BaseStage):
    """阶段20：时长控制 (Duration Control)"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.control_client = secondary_client
        self.control_client.model = ModelName.GLM_4_5.value

    @property
    def stage_number(self) -> int:
        return 20

    @property
    def stage_name(self) -> str:
        return "时长控制 (Duration Control)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage19_status = self.db_manager.get_stage_status(self.video_id, 19)
        if not stage19_status or stage19_status["status"] != "completed":
            return False, "阶段19 (AI精剪) 尚未完成"
        return True, ""

    def _parse_target_duration(self, target_duration_str: str) -> Optional[float]:
        """从字符串中解析出目标最大时长（秒）。"""
        if not target_duration_str:
            return None
        match_min = re.search(r"(\d+)\s*分钟", target_duration_str)
        if match_min:
            return float(match_min.group(1)) * 60
        match_range = re.findall(r"\d+", target_duration_str)
        if len(match_range) == 2:
            return float(match_range[1]) * 60 if "分钟" in target_duration_str else float(match_range[1])
        self.logger.warning(f"无法解析目标时长格式: '{target_duration_str}'")
        return None

    def _diagnose_script_durations(
        self, script: List[Dict[str, Any]], target_max_duration: float
    ) -> List[Dict[str, Any]]:
        """
        对整个剧本进行全面的时长诊断。
        为每个叙事单元计算：当前时长、视觉节拍数、时长预算、以及超出预算的量。
        """
        diagnostics = []
        total_visual_beats = sum(len(bc.get("visual_beats", [])) for bc in script)
        if total_visual_beats == 0:
            return []

        for i, beat_container in enumerate(script):
            duration = 0.0
            is_trimmable = False
            num_visual_beats = len(beat_container.get("visual_beats", []))

            for visual_beat in beat_container.get("visual_beats", []):
                shot_ids = visual_beat.get("selected_shot_order_ids") or []
                if len(shot_ids) > 1:
                    is_trimmable = True
                if shot_ids:
                    shots = self.db_manager.get_shots_by_order_ids(self.video_id, shot_ids)
                    duration += sum(s["end_time"] - s["start_time"] for s in shots)

            # 计算该单元的时长预算，基于其视觉节拍数占总数的比例
            proportional_budget = (num_visual_beats / total_visual_beats) * target_max_duration
            overshoot = duration - proportional_budget

            diagnostics.append(
                {
                    "beat_idx": i,
                    "duration": duration,
                    "num_visual_beats": num_visual_beats,
                    "is_trimmable": is_trimmable,
                    "proportional_budget": proportional_budget,
                    "overshoot": overshoot,
                }
            )
        return diagnostics

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行迭代式的、基于比例失衡的智能时长控制逻辑。"""
        output_type = "duration_controlled_script"

        # 1. 根据强制级别处理数据清理
        if force_level == "full":
            self.logger.info("强制模式 'full': 清理旧的时长控制后剧本数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, output_type)

        # 2. 确定要处理的剧本数据
        script_data = None
        if force_level == "soft":
            self.logger.info("--- 强制软执行模式 ---")
            # 尝试加载本阶段已有的输出，以在此基础上继续
            script_data = self.db_manager.get_stage_output(self.video_id, self.stage_number, output_type)
            if script_data:
                self.logger.info("发现已存在的时长控制后剧本，将在此基础上继续优化。")

        if not script_data:
            # 如果不是软模式，或软模式下没有找到旧数据，则检查是否可以直接跳过
            existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, output_type)
            if existing_output and not force_level:
                self.logger.info("从数据库加载已缓存的时长控制后剧本数据。")
                return True

            # 加载前一阶段的数据作为起点
            self.logger.info("从阶段19加载精剪后剧本作为起点。")
            script_data = self.db_manager.get_stage_output(self.video_id, 19, "refined_master_script")

        if not script_data or "refined_script_beats" not in script_data:
            self.logger.error("未找到有效的剧本数据（来自阶段19或阶段20的缓存）。")
            return False

        script_beats = script_data["refined_script_beats"]

        # 3. 处理目标时长
        target_duration_str = kwargs.get("target_duration")
        if not target_duration_str:
            self.logger.info("未提供 --target-duration 参数，跳过时长控制，直接传递剧本。")
            self.db_manager.save_stage_output(
                self.video_id, self.stage_number, self.stage_name, output_type, {"refined_script_beats": script_beats}
            )
            return True

        target_max_duration = self._parse_target_duration(target_duration_str)
        if target_max_duration is None:
            return False

        # 4. 迭代压缩循环 (逻辑保持不变)
        self.update_progress("正在检查并根据目标时长优化剪辑...")
        max_iterations = 99
        for i in range(max_iterations):
            diagnostics = self._diagnose_script_durations(script_beats, target_max_duration)
            if not diagnostics:
                break

            current_duration = sum(b["duration"] for b in diagnostics)

            if current_duration <= target_max_duration:
                self.logger.info(f"当前总时长 {current_duration:.2f}s 已满足目标。优化完成。")
                break

            overshoot = current_duration - target_max_duration
            self.logger.info(
                f"迭代 {i + 1}: 当前时长 {current_duration:.2f}s > 目标 {target_max_duration:.2f}s (超出 {overshoot:.2f}s)。"
            )

            candidates_to_trim = [b for b in diagnostics if b["overshoot"] > 0 and b["is_trimmable"]]
            if not candidates_to_trim:
                self.logger.warning("没有超出预算且可修剪的节拍，时长控制中止。")
                break

            beat_to_trim_info = max(candidates_to_trim, key=lambda x: x["overshoot"])
            beat_to_trim_idx = beat_to_trim_info["beat_idx"]
            beat_to_trim_data = script_beats[beat_to_trim_idx]
            current_beat_duration = beat_to_trim_info["duration"]

            trim_goal = current_beat_duration / 5.0

            self.update_progress(
                f"请求AI修剪最不成比例的叙事单元 (ID: {beat_to_trim_data['source_narrative_unit_number']}), "
                f"目标削减约 {trim_goal:.2f}s..."
            )

            trimmed_beat = self._trim_single_beat_with_ai(beat_to_trim_data, current_beat_duration, trim_goal)
            script_beats[beat_to_trim_idx] = trimmed_beat
        else:
            self.logger.warning(f"已达到最大压缩迭代次数 ({max_iterations})。")

        # 5. 保存最终结果 (逻辑保持不变)
        final_diagnostics = self._diagnose_script_durations(script_beats, target_max_duration)
        final_duration = sum(b["duration"] for b in final_diagnostics)
        self.logger.info(f"时长控制完成。最终成片预估时长: {final_duration:.2f}s")

        self.db_manager.save_stage_output(
            self.video_id, self.stage_number, self.stage_name, output_type, {"refined_script_beats": script_beats}
        )
        return True

    def _trim_single_beat_with_ai(
        self, beat_to_trim: Dict[str, Any], current_duration: float, trim_goal_seconds: float
    ) -> Dict[str, Any]:
        """调用AI对单个ScriptBeat进行修剪。"""
        try:
            prompt = BEAT_TRIM_PROMPT.format(
                beat_to_trim_json=json.dumps(beat_to_trim, ensure_ascii=False, indent=2),
                current_duration=current_duration,
                trim_goal_seconds=trim_goal_seconds,
                language="中文",
            )
            result = self.control_client.call_ai_with_tool(prompt, response_model=TrimmedBeatResponse)
            if result and result.trimmed_beat:
                self.logger.info(f"AI成功修剪了叙事单元 {beat_to_trim['source_narrative_unit_number']}。")
                return result.trimmed_beat.model_dump()
        except Exception as e:
            self.logger.error(
                f"AI修剪叙事单元 {beat_to_trim['source_narrative_unit_number']} 时失败: {e}", exc_info=True
            )

        self.logger.warning("AI修剪失败，将返回原始节拍作为后备。")
        return beat_to_trim
