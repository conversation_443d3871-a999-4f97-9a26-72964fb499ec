"""
阶段3：外部资料增强
通过两步研究法（规划+执行）进行深度信息挖掘。
"""

import re
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional

from config.schemas import ModelName
from config.settings import settings
from database.models import Research
from stages.base import BaseStage
from utils.ai_utils import primary_client, grok_client


class Stage3Research(BaseStage):
    """阶段3：外部资料增强"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)

        # 初始化用于执行研究的 Grok 客户端 (保持不变)
        if not grok_client:
            raise RuntimeError("Grok client not configured. Please check your .env file.")
        self.research_client = grok_client
        self.plan_client = primary_client
        self.research_client.model = ModelName.GROK_3_DEEPSEARCH.value
        self.plan_client.model = ModelName.GLM_4_5.value
        self.logger.debug(f"已初始化研究客户端 (模型: {self.research_client.model}) 和规划客户端 (模型: {self.plan_client.model})。")

    @property
    def stage_number(self) -> int:
        return 3  # 修改

    @property
    def stage_name(self) -> str:
        return "外部资料增强"  # 修改

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage2_status = self.db_manager.get_stage_status(self.video_id, 2)  # 修改
        if not stage2_status or stage2_status["status"] != "completed":
            return False, "阶段2（音频转写与分析）尚未完成"  # 修改
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行外部资料增强"""
        try:
            # --- 步骤 1: 强制模式处理 ---
            if force_level == "full":
                self.logger.info("强制模式 'full': 正在清理所有旧的研究数据。")
                self._clear_research_data()

            # --- 步骤 2: 获取执行参数 ---
            research_type = kwargs.get("research_type")
            topic_to_research = kwargs.get("topic")

            if not research_type:
                self.logger.error("未提供研究类型 (--type)，阶段无法执行。")
                return False

            if research_type == "personal":
                self.logger.info("视频类型为 'personal'，跳过外部资料研究。")
                return True

            title = kwargs.get("title")
            if not title:
                self.logger.error(f"研究类型 '{research_type}' 需要提供 --title 参数。")
                return False

            # --- 步骤 3: 生成研究计划 ---
            # 如果是 soft-force 且指定了 topic，则跳过计划生成，直接研究该 topic
            if force_level == "soft" and topic_to_research:
                self.logger.info("--- 强制软执行 / 定点重查模式 ---")
                self.logger.info(f"将强制重新研究特定主题: '{topic_to_research}'")
                all_topics = [topic_to_research]
            else:
                self.logger.info("生成并执行完整的研究计划。")
                plan = self._generate_research_plan(focus_on_creators=False, **kwargs)
                if not plan:
                    self.logger.error("无法生成研究计划，阶段失败。")
                    return False
                all_topics = [topic for category_topics in plan.values() for topic in category_topics]

            # --- 步骤 4: 执行研究 ---
            total_topics = len(all_topics)
            self.logger.info(f"研究计划已生成，共包含 {total_topics} 个研究主题。开始并行执行（并发数：3）...")

            completed_count = 0
            with ThreadPoolExecutor(max_workers=3) as executor:
                # 注意：传递 force_level，以便在非强制模式下跳过已存在的主题，在强制模式下重新执行
                future_to_topic = {
                    executor.submit(self._process_topic, topic, force_level): topic for topic in all_topics
                }
                for future in as_completed(future_to_topic):
                    topic = future_to_topic[future]
                    try:
                        status = future.result()
                        completed_count += 1
                        progress_msg = f"进度 {completed_count}/{total_topics} | 主题: {topic[:30]}... [{status}]"
                        self.update_progress(progress_msg)
                        self.logger.info(progress_msg)
                    except Exception as exc:
                        completed_count += 1
                        self.logger.error(f"主题 '{topic[:30]}...' 在执行中产生严重错误: {exc}", exc_info=True)
                        self.update_progress(f"进度 {completed_count}/{total_topics} | 主题: {topic[:30]}... [ERROR]")

            self.logger.info("所有研究任务已处理完毕。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _process_topic(self, topic: str, force_level: Optional[str]) -> str:
        """
        处理单个研究主题的完整流程：检查、执行、保存。
        内置两层重试逻辑：
        1. 外层循环：处理内容级别的失败（如Grok输出截断）。
        2. 内层（AIClient）：处理网络/API级别的失败。
        返回处理状态 ('skipped', 'completed', 'failed')。
        """
        if not force_level and self._research_exists(topic):
            self.logger.debug(f"研究主题 '{topic[:30]}...' 已存在，跳过。")
            return "skipped"

        # --- 【核心改动】外层手动重试循环，用于内容验证 ---
        for attempt in range(settings.MAX_RETRIES):
            try:
                # AIClient 内部已经有其自身的重试逻辑（针对网络/API错误）
                report = self.research_client.call_ai(topic, extra_body={"thinking": {"type": "deepsearch"}})

                # --- 【核心改动】内容验证逻辑 ---
                # 检查Grok输出是否被截断
                if report and report.startswith("<think>") and "</think>" not in report:
                    self.logger.warning(
                        f"检测到Grok输出被截断 (尝试 {attempt + 1}/{settings.MAX_RETRIES})。主题: '{topic[:30]}...'"
                    )
                    # 如果不是最后一次尝试，则等待并进入下一次循环
                    if attempt < settings.MAX_RETRIES - 1:
                        time.sleep(settings.RETRY_DELAY)
                        continue  # 继续下一次重试
                    else:
                        # 如果是最后一次尝试，则记录错误并跳出循环，最终将返回 "failed"
                        self.logger.error("Grok输出在所有重试后仍然被截断。")
                        report = None  # 将报告置空，表示失败

                # --- 正常的成功/失败处理 ---
                if report:
                    # 清理报告，移除 <think> 标签及其内容
                    cleaned_summary = re.sub(r"<think>.*?</think>", "", report, flags=re.DOTALL).strip()
                    self._save_research_result(topic, report, cleaned_summary)
                    # 在每次成功的API调用后短暂休眠，避免因并发导致过于频繁的请求
                    time.sleep(1)
                    return "completed"  # 成功，直接返回并结束函数
                else:
                    # 如果 report 为空（可能是因为截断或API返回空），则跳出循环
                    break

            except Exception as e:
                # 这里的异常是经过AIClient重试后仍然失败的最终异常
                self.logger.error(f"研究主题 '{topic[:30]}...' 的AI调用在所有内部重试后仍然失败。错误: {e}")
                # 跳出循环，最终将返回 "failed"
                break

        # 如果循环正常结束（即所有重试都失败了），则返回失败
        self.logger.error(f"研究主题 '{topic[:30]}...' 处理失败。")
        return "failed"

    def _clear_research_data(self):
        """清理此视频之前的所有研究数据"""
        with self.db_manager.get_session() as session:
            session.query(Research).filter_by(video_id=self.video_id).delete()
            self.logger.info("已清理旧的研究数据。")

    def _generate_research_plan(self, focus_on_creators: bool = False, **kwargs) -> Optional[Dict[str, List[str]]]:
        """使用通用AI生成详细的研究计划，并与预设的核心主题合并。"""
        from config.prompts import RESEARCH_PLAN_PROMPT
        from config.schemas import ResearchPlanResponse

        title = kwargs.get("title") or "未知"
        year = kwargs.get("year") or "未知"
        director = kwargs.get("director") or "未知"
        actors_list = kwargs.get("actors", [])
        actors = ", ".join(actors_list) or "未知"

        # --- 步骤 1: 构建统一的电影信息前缀 ---
        movie_identifier = f"电影《{title}》"
        if year != "未知":
            movie_identifier += f" ({year})"

        director_context = f"，由 {director} 执导" if director != "未知" else ""
        actors_context = f"，主演包括 {actors}" if actors != "未知" else ""

        full_movie_prefix = f"{movie_identifier}{director_context}{actors_context} "

        # --- 步骤 2: 构建带有完整前缀的核心研究主题 ---
        core_topics = [
            f"{full_movie_prefix}获取并列出其完整演职人员名单，包括所有主要和次要角色。",
            f"{full_movie_prefix}详细解读其主线故事情节、关键转折点和结局。",
            f"{full_movie_prefix}深入分析其创作背景，例如其灵感来源、拍摄期间的趣闻轶事，以及它在当时的历史或文化背景。",
            f"{full_movie_prefix}探讨其所表达的核心主题、象征意义，以及它在电影史上的地位或对特定类型片的影响。",
            f"{full_movie_prefix}收集并整理其上映后的公众反响和来自知名影评人的专业评价。",
        ]

        # --- 步骤 3: 将核心主题告知AI，让它生成补充性主题 (逻辑保持不变) ---
        prompt = RESEARCH_PLAN_PROMPT.format(
            title=title,
            year=year,
            director=director,
            actors=actors,
            existing_topics="\n".join([f"- {topic}" for topic in core_topics]),
            language=settings.SCRIPT_LANGUAGE,
        )

        try:
            ai_generated_plan = self.plan_client.call_ai_with_tool(prompt, response_model=ResearchPlanResponse)
        except Exception as e:
            self.logger.error(f"AI生成补充性研究计划失败: {e}")
            ai_generated_plan = ResearchPlanResponse(
                core_analysis=[],
                background_and_worldview=[],
                creators_research=[],
                reception_and_legacy=[],
            )

        # --- 【核心改动】步骤 4: 合并并为所有主题添加前缀 ---
        final_plan = {}

        # 为AI生成的每个主题添加前缀
        for category, topics in ai_generated_plan.model_dump().items():
            final_plan[category] = [f"{full_movie_prefix}{topic}" for topic in topics]

        # 确保 core_analysis 类别存在
        if "core_analysis" not in final_plan:
            final_plan["core_analysis"] = []

        # 使用集合来高效地将核心主题去重并合并
        existing_topics_set = set(final_plan["core_analysis"])
        for topic in reversed(core_topics):
            if topic not in existing_topics_set:
                final_plan["core_analysis"].insert(0, topic)
                existing_topics_set.add(topic)

        self.logger.info(f"已将 {len(core_topics)} 个核心研究任务合并到最终研究计划中。")

        # --- 步骤 5: 如果是精简模式，则进行过滤 (逻辑保持不变) ---
        if focus_on_creators:
            creators_plan = {}
            if "creators_research" in final_plan:
                creators_plan["creators_research"] = final_plan["creators_research"]
            if "core_analysis" in final_plan:
                creators_plan["core_analysis"] = final_plan["core_analysis"]

            self.logger.info("已生成精简版研究计划，仅包含主创人员和核心分析。")
            return creators_plan

        return final_plan

    def _research_exists(self, topic: str) -> bool:
        """检查数据库中是否已存在该研究主题的完成记录"""
        from database.models import Research

        with self.db_manager.get_session() as session:
            return (
                session.query(Research)
                .filter_by(video_id=self.video_id, related_entity=topic, status="completed")
                .first()
                is not None
            )

    def _save_research_result(self, topic: str, raw_report: str, cleaned_summary: str):
        """保存研究结果，区分原始报告和清理后的摘要"""
        from database.models import Research

        with self.db_manager.get_session() as session:
            new_research = Research(
                video_id=self.video_id,
                related_entity=topic,  # 将研究主题作为实体
                source_url="Grok DeepSearch",
                retrieved_content=raw_report,  # 存储完整的原始报告
                ai_summary=cleaned_summary,  # 存储清理后的摘要
                status="completed",
            )
            session.add(new_research)
