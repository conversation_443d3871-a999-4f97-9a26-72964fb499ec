"""
阶段16：D2S 重写者模块 (Rewriter)
第一步：剧本创作策略规划
"""

import json
from typing import Any, Dict, Optional

from config.prompts import SCRIPT_WRITING_STRATEGY_PROMPT
from config.schemas import ModelName, ScriptWritingStrategyResponse
from config.settings import settings
from stages.base import BaseStage
from database.models import Scenes
from utils.ai_utils import secondary_client
from utils.enhanced_base_stage import EnhancedBaseStage


class Stage16ScriptStrategy(BaseStage, EnhancedBaseStage):
    """阶段16：剧本创作策略规划"""

    def __init__(self, db_manager, video_id):
        # 显式初始化BaseStage
        BaseStage.__init__(self, db_manager, video_id)
        # 显式初始化EnhancedBaseStage
        EnhancedBaseStage.__init__(self, db_manager, video_id)
        self.strategy_client = secondary_client
        self.strategy_client.model = ModelName.GLM_4_5.value

    @property
    def stage_number(self) -> int:
        return 16

    @property
    def stage_name(self) -> str:
        return "D2S Rewriter (策略规划)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 依赖于阶段14（故事大纲生成）的完成
        stage14_status = self.db_manager.get_stage_status(self.video_id, 14)
        if not stage14_status or stage14_status["status"] != "completed":
            return False, "阶段14 (D2S Rewriter 大纲生成) 尚未完成"

        story_outline_data = self.db_manager.get_stage_output(self.video_id, 14, "story_outline")
        if not story_outline_data:
            return False, "数据库中未找到故事大纲数据"

        # 依赖于阶段15 (角色档案) 完成
        dossier_status = self.db_manager.get_stage_status(self.video_id, 15)
        if not dossier_status or dossier_status["status"] != "completed":
            return False, "阶段15（角色档案生成）尚未完成"

        # 依赖于阶段10 (数据基础构建) 完成以获取 master_context
        stage10_status = self.db_manager.get_stage_status(self.video_id, 10)
        if not stage10_status or stage10_status["status"] != "completed":
            return False, "阶段10 (数据基础构建) 尚未完成"

        creative_brief = self.db_manager.get_stage_output(self.video_id, 10, "creative_brief")
        if not creative_brief:
            return False, "数据库中未找到创作说明书数据"

        if not self.db_manager.get_all_research_summaries(self.video_id):
            return False, "数据库中未找到研究资料摘要"

        return True, ""

    def _format_dossier_for_prompt(self, dossier_data: Dict[str, Any]) -> str:
        """将JSON格式的角色档案转换为格式化的纯文本，用于提示词。"""
        if not dossier_data or "dossiers" not in dossier_data:
            return "无角色档案。"

        parts = []
        for dossier in dossier_data["dossiers"]:
            parts.append(
                f"character_id: {dossier.get('character_id')}\n"
                f"name: {dossier.get('name')}\n"
                f"background: {dossier.get('background')}\n"
                f"motivation: {dossier.get('motivation')}\n"
                f"conflict: {dossier.get('conflict')}\n"
                f"arc: {dossier.get('arc')}"
            )
        return "\n\n".join(parts)

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行阶段处理，使用增强功能"""
        return self.execute_with_enhancement(force_level, **kwargs)

    def _execute_enhanced(
        self, cross_stage_context: Dict[str, Any], force_level: Optional[str] = None, **kwargs
    ) -> bool:
        """带跨阶段上下文的增强执行方法。"""
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的剧本创作策略数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "script_strategy")

        # 检查数据库中是否已有输出
        existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, "script_strategy")
        if existing_output:
            self.logger.info("从数据库加载已缓存的剧本创作策略数据。")
            return True

        # 1. 从数据库加载所需数据
        try:
            story_outline_data = self.db_manager.get_stage_output(self.video_id, 14, "story_outline")
            master_context = self.db_manager.get_stage_output(self.video_id, 10, "creative_brief")
            research_summary = self.db_manager.get_all_research_summaries(self.video_id)
            dossier_data = self.db_manager.get_stage_output(self.video_id, 15, "character_dossier") or {}
            dossier_text = self._format_dossier_for_prompt(dossier_data)
            tagline_data = self.db_manager.get_stage_output(self.video_id, 4, "tagline") or {}
            tagline = tagline_data.get("tagline", "无")

            dossier_status = self.db_manager.get_stage_status(self.video_id, 15)
            if not dossier_status or dossier_status["status"] != "completed":
                self.logger.error("前置检查失败：阶段15（角色档案生成）尚未完成。")
                return False
            if not story_outline_data or not master_context:
                self.logger.error("无法从数据库加载前置阶段的输出数据。")
                return False
            if not research_summary or research_summary == "没有可用的研究资料。":
                self.logger.error("无法从数据库加载研究资料摘要。")
                return False

        except Exception as e:
            self.logger.error(f"无法加载前置数据: {e}")
            return False

        # --- 【核心修改】校验故事大纲中的场景编号是否存在 ---
        if story_outline_data and story_outline_data.get("story_outline"):
            with self.db_manager.get_session() as session:
                existing_scene_numbers = {
                    s.scene_number for s in session.query(Scenes.scene_number).filter_by(video_id=self.video_id).all()
                }

            for scene_outline_item in story_outline_data["story_outline"]:
                scene_num = scene_outline_item.get("narrative_unit_number")
                if scene_num not in existing_scene_numbers:
                    self.logger.error(
                        f"数据不一致错误：故事大纲中的叙事单元编号 {scene_num} 在数据库中找不到对应的场景(Scene)。"
                        "这可能是阶段14 (大纲生成) 的AI输出与实际场景不符导致的。请考虑重新运行阶段14。"
                    )
                    return False  # 作为一个致命错误，中断执行
        # --- 核心修改结束 ---

        # 2. 调用AI生成创作策略
        self.update_progress("AI正在规划全局剧本创作策略...")
        try:
            perspective = master_context.get("project_info", {}).get("narration_perspective", "third_person")
            prompt = SCRIPT_WRITING_STRATEGY_PROMPT.format(
                tagline=tagline,
                dossier_text=dossier_text,
                research_summary=research_summary,
                story_outline_json=json.dumps(story_outline_data, ensure_ascii=False, indent=2),
                project_info_json=json.dumps(master_context.get("project_info"), ensure_ascii=False, indent=2),
                characters_json=json.dumps(master_context.get("characters"), ensure_ascii=False, indent=2),
                language=settings.SCRIPT_LANGUAGE,
                narration_perspective=perspective,
            )

            result = self.strategy_client.call_ai_with_tool(prompt, response_model=ScriptWritingStrategyResponse)

            if not result:
                self.logger.error("AI未能生成任何有效的创作策略。")
                return False

            # 3. 保存到数据库
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="script_strategy",
                output_data=result.model_dump(),
            )

            self.logger.info("✅ 成功生成剧本创作策略并保存到数据库。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False
