"""
AI JSON输出的Pydantic模型定义
用于强制AI返回结构化的、可预测的、经过验证的对象。
"""

from enum import Enum
from typing import Any, List, Optional

from pydantic import BaseModel, Field, model_validator


# --- 新增：单个视觉节拍模型 ---
class ModelName(str, Enum):
    DOUBAO_SEED_1_6 = "doubao-seed-1-6-250615"
    DOUBAO_SEED_1_6_THINKING = "doubao-seed-1-6-thinking-250615"
    DEEPSEEK_R1 = "deepseek-r1-250528"
    O3_2025_04_16 = "o3-2025-04-16"
    KIMI_K2 = "kimi-k2-0711-preview"
    GLM_4_5 = "ZhipuAI/GLM-4.5"
    GROK_3_DEEPSEARCH = "grok-3-deepsearch"


# --- 新增：单个视觉节拍模型 ---
class VisualBeat(BaseModel):
    source_scene_number: int = Field(..., description="此视觉节拍对应的源场景编号。")
    description: str = Field(..., description="基于源场景摘要生成的、连贯的视觉描述。")
    selected_shot_order_ids: Optional[List[int]] = Field(
        None, description="由AI剪辑师在精剪阶段（阶段19）最终选定的、用于呈现此视觉节拍的镜头顺序ID列表。"
    )


# --- 微观场景分析 ---
class ShotAnalysisModel(BaseModel):
    shot_type: str = Field(..., description="景别 (从 特写, 近景, 中景, 全景, 远景 中选择)。")
    camera_angle: str = Field(..., description="机位角度 (从 平视, 俯视, 仰视 中选择)。")
    camera_movement: str = Field(..., description="运镜方式 (从 固定镜头, 推, 拉, 摇, 移, 跟, 升降 中选择)。")
    composition: str = Field(..., description="构图特点 (例如：三分法, 对称构图等)。")
    lighting: str = Field(..., description="光线风格 (例如：高调, 低调, 自然光等)。")


class SceneAnalysisResponse(BaseModel):
    visual_description: str = Field(
        ..., description="对整个镜头画面的综合性、叙事性、文学性的描述，总结核心视觉元素、氛围和潜在的象征意义。"
    )
    narrative_function: str = Field(
        ..., description="解读该镜头在叙事结构中的核心功能。例如：'建立镜头'、'角色引入'、'关键转折'、' foreshadowing(预示)'、'情绪锚点'等。"
    )
    aesthetic_analysis: str = Field(
        ..., description="对镜头美学风格的分析，包括色彩运用、光影情绪、构图的艺术表达等，解读其如何服务于影片的整体美学。"
    )
    people: str = Field(..., description="识别画面中的主要人物，并深入描述他们的状态、微表情或肢体语言所揭示的内在信息。")
    setting: str = Field(..., description="详细描述场景环境、地点和时间，并分析环境如何反映或影响角色的心境。")
    main_action: str = Field(..., description="描述该镜头内的核心动作或事件，并点出其对情节的推动作用。")
    emotion: str = Field(
        ...,
        description="分析并提炼该镜头所传达的核心情绪或情感基调，不仅识别表面情绪，更要推断其背后的复杂情感。",
    )
    key_objects: str = Field(..., description="列出与核心动作或具有叙事/象征意义的关键物体。")
    on_screen_text: str = Field(
        ..., description="识别并转录画面中出现的任何重要文字。如果没有则返回空字符串。"
    )
    shot_analysis: ShotAnalysisModel = Field(..., description="包含详细镜头语言技术参数分析的对象。")
    genre_specific_analysis: Optional[str] = Field(
        None, description="根据影片类型进行的特定分析。例如动作片的激烈程度、剧情片的潜台词、喜剧片的笑点类型等。"
    )


# --- 镜头到场景聚合 ---
class SceneGroupingItem(BaseModel):
    start_shot_id: int = Field(..., description="组成这个场景的第一个镜头的ID。")
    end_shot_id: int = Field(..., description="组成这个场景的最后一个镜头的ID。")
    summary: str = Field(..., description="对这个场景的整体内容进行简洁的叙事性总结。")
    narrative_purpose: str = Field(..., description="总结这个场景在故事中的作用。")


class SceneGroupingResponse(BaseModel):
    scenes: List[SceneGroupingItem]


# --- 场景到序列分析 ---
class SequenceAnalysisItem(BaseModel):
    scene_numbers: List[int] = Field(..., description="组成这个序列的所有原始场景编号。")
    theme: str = Field(..., description="这个序列的核心主题或中心思想。")
    summary: str = Field(..., description="对这个序列的完整情节进行连贯的叙事性总结。")
    emotional_arc: str = Field(..., description="描述这个序列从开始到结束的情绪变化曲线。")


class SequenceAnalysisResponse(BaseModel):
    sequences: List[SequenceAnalysisItem]


# --- 新增：角色情感弧线分析模型 ---
class CharacterEmotionalArc(BaseModel):
    character_name: str = Field(..., description="角色的名称。")
    emotional_arc: str = Field(..., description="该角色在此序列中的情感变化或心路历程的详细描述。")


class CharacterEmotionalArcResponse(BaseModel):
    character_arcs: List[CharacterEmotionalArc]


# --- D2S Rewriter: 故事大纲模型 ---
class StoryOutlineItem(BaseModel):
    narrative_unit_number: int = Field(..., description="叙事单元的编号，从1开始。")
    narrative_goal: str = Field(..., description="该叙事单元在故事中的核心目标或作用。")
    characters_present: List[str] = Field(..., description="在此场景中出现的角色的character_id列表。")
    setting: str = Field(..., description="场景发生的地点和时间。")
    candidate_scene_ids: List[int] = Field(..., description="可用于视觉化呈现该场景的一组候选源场景的数字ID列表。")
    summary: str = Field(
        ...,
        description="对该场景内容的详细段落描述。请详细阐述场景的起因、经过、结果，以及关键角色的行动和反应，确保每个场景的描述都足够丰富，能够独立成段。",
    )


class StoryOutlineResponse(BaseModel):
    story_outline: List[StoryOutlineItem]


# --- 新增：剧本创作策略模型 ---
class ScriptWritingStrategyResponse(BaseModel):
    global_narration_tone: str = Field(..., description="整部影片的全局旁白基调和风格。")
    hook_strategy: Optional[str] = Field(None, description="视频开头的“钩子”策略，用于迅速抓住观众注意力。")
    climax_strategy: Optional[str] = Field(None, description="视频高潮部分的核心论点或关键转折的揭示策略。")
    conclusion_strategy: Optional[str] = Field(None, description="视频结尾的总结与主题升华策略。")


# --- 新增：D2S 场景镜头选择模型 ---
class ShotSelectionForSceneResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(
        ..., description="AI剪辑师最终决定使用的、按时间顺序排列的镜头 order_id 列表。"
    )
    justification: str = Field(..., description="AI做出这个剪辑选择的简要理由。")


# --- 新增：D2S 故事流编排模型 ---
class StoryFlowItem(BaseModel):
    narrative_unit_number: int = Field(..., description="来自原始故事大纲的叙事单元编号。")
    reasoning: str = Field(
        ..., description="将此单元放置在当前位置（例如：开篇、主体、高潮、结尾）的理由，需要与全局策略关联。"
    )


class StoryFlowResponse(BaseModel):
    story_flow: List[StoryFlowItem] = Field(
        ..., description="根据全局策略重新编排后的故事流程，这是一个叙事单元对象的列表。"
    )


# --- 电影解说文案 ---
class NarrationType(str, Enum):
    NARRATOR = "NARRATOR"
    INNER_MONOLOGUE = "INNER_MONOLOGUE"
    CHARACTER_DIALOGUE = "CHARACTER_DIALOGUE"


class ScriptBeat(BaseModel):
    beat_number: int = Field(..., description="叙事单元在整个剧本中的顺序编号，从1开始。")
    source_narrative_unit_number: int = Field(..., description="此剧本节拍对应的源叙事单元编号。")
    visual_beats: List[VisualBeat] = Field(
        ...,
        description="构成此叙事单元的、按时间顺序排列的纯视觉节拍列表。在阶段19后，此列表中的每个VisualBeat将包含`selected_shot_order_ids`字段。",
    )
    audio_content: Optional[str] = Field(
        None, description="对此整个叙事单元的综合旁白。在纯视觉剪辑阶段，此字段应为null。"
    )
    narration_type: Optional[NarrationType] = Field(
        None,
        description="音频的性质。在纯视觉剪辑阶段，此字段应为null。",
    )

    @model_validator(mode="after")
    def check_audio_logic(self) -> "ScriptBeat":
        if self.audio_content and self.narration_type is None:
            raise ValueError("如果提供了 'audio_content', 则必须指定 'narration_type'。")
        if self.narration_type and self.audio_content is None:
            raise ValueError("如果指定了 'narration_type', 则必须提供 'audio_content'。")
        return self


ScriptBeat.model_rebuild()  # 确保在所有模型定义完成后重建


class MovieCommentaryScriptResponse(BaseModel):
    script: List[Any] = Field(..., description="构成整个视频的音画节拍列表。")

    @model_validator(mode="after")
    def validate_script_beats(self):
        # 确保列表中的每个元素都是 ScriptBeat 类型
        from pydantic import ValidationError

        for i, item in enumerate(self.script):
            if not isinstance(item, ScriptBeat):
                try:
                    self.script[i] = ScriptBeat.model_validate(item)
                except ValidationError as e:
                    raise ValueError(f"Script item at index {i} is not a valid ScriptBeat: {e}")
        return self


# --- 新增：D2S 单个叙事单元修剪模型 ---
class TrimmedBeatResponse(BaseModel):
    trimmed_beat: ScriptBeat = Field(..., description="经过智能修剪后，返回的完整的、更新后的 ScriptBeat 对象。")


# --- 音画映射 ---
# SentenceToSceneMapperResponse 不再需要，但为了兼容性保留


# --- 研究计划 ---
class ResearchPlanResponse(BaseModel):
    core_analysis: List[str]
    background_and_worldview: List[str]
    creators_research: List[str]
    reception_and_legacy: List[str]


# --- 音频分析 ---
class AudioAnalysisResponse(BaseModel):
    transcript: str = Field(..., description="将所有可识别的对话转录为文字。")
    speaker_tone: str = Field(..., description="描述说话者的语气和情感。")
    music_analysis: str = Field(..., description="分析背景音乐的风格和营造的氛围。")
    key_sound_events: List[str] = Field(..., description="列出除对话和音乐外的关键声音事件。")


# 新增：孤儿镜头修复决策模型
class OrphanFixDecisionEnum(str, Enum):
    PRECEDING = "preceding"
    SUCCEEDING = "succeeding"
    NEW_SCENE = "new_scene"


# --- 【核心改动】更新孤儿镜头修复决策模型，使用枚举代替pattern ---
class OrphanFixDecision(BaseModel):
    decision: OrphanFixDecisionEnum = Field(
        ...,
        description="你的决策。必须是 'preceding', 'succeeding', 或 'new_scene' 中的一个。",
    )


# --- D2S: 设计文档解析模型 ---
class RelationshipInfo(BaseModel):
    character_id: str = Field(..., description="关联角色的ID。", alias="target_character_id")
    relationship_type: str = Field(..., description="关系类型，例如 '盟友', '敌人', '导师'。", alias="nature")


class CharacterInfo(BaseModel):
    character_id: str = Field(..., description="角色的唯一标识符，例如 'char_anna'。")
    name: str = Field(..., description="角色的名字。")
    description: str = Field(..., description="角色的外貌、性格和背景故事的详细描述。")
    motivations: str = Field(..., description="驱动角色行动的核心欲望或目标。", alias="motivation")
    relationships: List[RelationshipInfo] = Field(..., description="描述该角色与其他角色的关系。")


class ProjectInfo(BaseModel):
    title: str = Field(..., description="项目的正式标题。", alias="project_title")
    logline: str = Field(
        ...,
        description="用一个包含五句话的段落来概括整个故事。第一句介绍背景和主角，接下来三句描述三个核心的冲突或转折点，最后一句总结结局。",
    )
    themes: List[str] = Field(
        ..., description="故事探讨的核心主题，例如 ['忠诚', '牺牲', '成长']。", alias="core_themes"
    )
    target_audience: str = Field(..., description="描述视频的目标观众群体及其特征。")
    narrative_goals: List[str] = Field(..., description="故事旨在传达的关键信息或和实现的情感效果。")
    narration_perspective: str = Field(..., description="叙事视角，例如 'first_person' 或 'third_person'。")


class DesignDocParseResponse(BaseModel):
    project_info: ProjectInfo
    characters: List[CharacterInfo]

    @model_validator(mode="before")
    def restructure_input(cls, data: Any) -> Any:
        if isinstance(data, dict):
            # --- 步骤 1: 重构 project_info ---
            project_overview = data.get("project_overview")
            if "project_info" not in data and project_overview:
                # 初始化一个临时的 project_info 字典
                temp_project_info = {}

                # 从 project_overview 的顶层提取信息
                temp_project_info["target_audience"] = project_overview.get("target_audience")
                temp_project_info["narrative_goals"] = [project_overview.get("core_purpose")]

                # 从内嵌的 original_film_info 提取信息
                original_info = project_overview.get("original_film_info", {})
                temp_project_info["title"] = original_info.get("title")
                temp_project_info["logline"] = original_info.get("core_plot")

                # 从顶层的 core_themes 提取信息
                temp_project_info["themes"] = data.get("core_themes")

                # 将重构好的字典赋给 project_info
                data["project_info"] = {k: v for k, v in temp_project_info.items() if v is not None}

            # --- 步骤 2: 重构 characters ---
            # 同时检查 "character_settings" 和 "character_setup"
            character_settings = data.get("character_settings") or data.get("character_setup")
            if "characters" not in data and character_settings:
                data["characters"] = character_settings

            # --- 步骤 3: 【核心改动】重构并注入 relationships ---
            character_relationships = data.get("character_relationships")
            if character_relationships and data.get("characters"):
                # 创建一个从 character_id 到角色信息字典的映射，以便快速查找
                char_map = {char.get("character_id"): char for char in data["characters"]}

                for rel in character_relationships:
                    source_id = rel.get("source_id")
                    target_id = rel.get("target_id")

                    # 找到源角色
                    if source_id in char_map:
                        source_char = char_map[source_id]
                        # 如果源角色还没有 relationships 列表，则创建一个
                        if "relationships" not in source_char:
                            source_char["relationships"] = []

                        # 构建符合我们模型的关系对象
                        new_relationship = {
                            "target_character_id": target_id,
                            "nature": rel.get("type"),  # 使用别名 'nature' 来匹配 'relationship_type'
                            # 'description' 字段在我们的模型中不存在，所以忽略它
                        }
                        source_char["relationships"].append(new_relationship)
        return data


# --- D2S Reader: 事件识别模型 ---
class NarrativeEvent(BaseModel):
    event_id: Optional[str] = Field(None, description="事件的唯一ID，由系统在后处理中分配，格式为 'event_XXX'。")
    event_description: str = Field(..., description="对这个叙事事件的简洁、高度概括的描述。")
    importance_score: float = Field(
        ..., ge=0.0, le=1.0, description="该事件的叙事重要性评分 (0.0-1.0)，用于过滤次要事件。"
    )
    psychological_motivation: str = Field(..., description="驱动此事件发生的核心角色的主要心理动机或内在状态。")
    characters_present: List[str] = Field(..., description="在此事件中出现的角色的名字列表。")
    source_scene_numbers: List[int] = Field(..., description="构成这个事件的一个或多个源场景的数字编号列表。")


class EventIdentificationResponse(BaseModel):
    narrative_events: List[NarrativeEvent]


# --- 新增：D2S 角色档案模型 ---
class CharacterDossierItem(BaseModel):
    character_id: str = Field(..., description="角色的唯一ID。")
    name: str = Field(..., description="角色的名字。")
    background: str = Field(..., description="角色的背景故事和关键经历。")
    motivation: str = Field(..., description="驱动角色的核心动机。")
    conflict: str = Field(..., description="角色面临的主要内心或外部冲突。")
    arc: str = Field(..., description="角色在故事中经历的转变或成长弧光。")


class CharacterDossierResponse(BaseModel):
    dossiers: List[CharacterDossierItem] = Field(..., description="所有角色的档案列表。")


# --- D2S Reader: 因果链接推断模型 ---
class CausalLink(BaseModel):
    source_event_id: str = Field(..., description="因果关系中的源头事件ID。")
    target_event_id: str = Field(..., description="因果关系中的目标事件ID。")
    causality_description: str = Field(..., description="对这条因果关系的简要描述和推理依据。")


class CausalLinkInferenceResponse(BaseModel):
    causal_links: List[CausalLink]


# --- D2S: 剧本评估模型 (REACT-S框架) ---
class ScriptEvaluationScore(BaseModel):
    score: int = Field(..., ge=1, le=5, description="评分 (1-5分)。")
    justification: str = Field(..., description="评分的详细理由。")


class ScriptEvaluationResponse(BaseModel):
    relevance: ScriptEvaluationScore = Field(
        ..., description="关联性 (Relevance): 剧本在多大程度上回应和实现了项目目标和主题？"
    )
    engagement: ScriptEvaluationScore = Field(
        ..., description="吸引力 (Engagement): 剧本是否拥有引人入胜的节奏、生动的对白和坚实的结构？"
    )
    adherence: ScriptEvaluationScore = Field(
        ..., description="遵循度 (Adherence): 角色行为和对白是否严格遵循了角色设定和动机？"
    )
    coherence: ScriptEvaluationScore = Field(
        ..., description="连贯性 (Coherence): 故事的情节发展是否符合逻辑？因果链条在剧本中是否得到了可信的呈现？"
    )
    technical_quality: ScriptEvaluationScore = Field(
        ...,
        description="技术性 (Technical-quality): 剧本格式是否规范？动作和声音描述是否清晰，足以指导后续的AI剪辑决策？",
    )


# --- 新增：镜头序列选择模型 ---
class ShotSequenceSelectionResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(..., description="AI选择的最佳镜头序列的顺序ID列表。")
    justification: str = Field(..., description="AI选择这个序列的理由。")


# --- 新增：标语生成模型 ---
class TaglineResponse(BaseModel):
    tagline: str = Field(..., description="为视频生成的一句简洁、有力、吸引人的宣传标语。")


# --- 新增：D2S 粗剪精炼响应模型 ---
class RoughCutRefinementResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(
        ..., description="AI剪辑师最终决定使用的、按时间顺序排列的镜头 order_id 列表。"
    )
    justification: str = Field(..., description="AI做出这个剪辑选择的简要理由。")


# --- 新增：D2S 角色补全响应模型 ---
class CharacterCompletionResponse(BaseModel):
    completed_characters: List[CharacterInfo] = Field(..., description="一个包含新补充的、完整的角色信息对象的列表。")


# --- 新增：D2S 单场景剧本评估响应模型 ---
class SceneScriptEvaluationResponse(BaseModel):
    score: int = Field(..., ge=1, le=5, description="对该场景剧本的总体评分 (1-5分)。")
    is_ready_for_production: bool = Field(..., description="判断该稿件是否已达到可用于生产的质量标准。")
    justification: str = Field(..., description="评分的简要理由。")
    suggested_improvements: str = Field(..., description="具体的、可操作的修改建议，用于指导下一轮修订。")


# --- 新增：镜头内容增强响应模型 ---
class ShotEnrichmentResponse(SceneAnalysisResponse):
    """
    基于宏观上下文，对单个镜头的完整分析进行增强后的响应模型。
    它继承了 SceneAnalysisResponse 的所有字段，并增加了一个理由字段。
    """

    justification: str = Field(
        ..., description="简要说明你为什么这样修改，以及新描述如何更好地服务于场景和序列的目标。"
    )


# ============== 智能剪辑优化相关模型 ==============


# --- 智能剪辑质量评估模型 ---
class DimensionScore(BaseModel):
    """单个维度的评分"""

    score: float = Field(..., ge=0.0, le=5.0, description="该维度的评分 (0-5分)")
    justification: str = Field(..., description="该维度评分的具体理由")


class IntelligentEditingEvaluationResponse(BaseModel):
    """智能剪辑质量评估响应"""

    overall_score: float = Field(..., ge=0.0, le=5.0, description="综合评分 (0-5分)")

    duration_control: DimensionScore = Field(..., description="时长控制维度评分")
    material_matching: DimensionScore = Field(..., description="素材匹配度维度评分")
    rhythm_coherence: DimensionScore = Field(..., description="节奏连贯性维度评分")
    visual_coherence: DimensionScore = Field(..., description="视觉连贯性维度评分")
    audio_video_sync: DimensionScore = Field(..., description="音画同步维度评分")

    critical_issues: List[str] = Field(..., description="识别出的致命问题列表")
    optimization_suggestions: List[str] = Field(..., description="优化建议列表")

    pass_threshold: float = Field(default=4.0, description="通过验证的最低分数阈值")
    is_acceptable: bool = Field(..., description="该剪辑方案是否可以接受")


# --- 节奏优化模型 ---
class RhythmAdjustment(BaseModel):
    """单个节拍的节奏调整方案"""

    beat_index: int = Field(..., description="节拍索引")
    original_duration: float = Field(..., description="原始时长(秒)")
    new_duration: float = Field(..., description="调整后时长(秒)")
    adjustment_reason: str = Field(..., description="调整理由")


class RhythmOptimizationResponse(BaseModel):
    """节奏优化响应"""

    rhythm_score: float = Field(..., ge=0.0, le=5.0, description="当前节奏质量评分")
    needs_optimization: bool = Field(..., description="是否需要进行节奏优化")

    target_avg_duration: float = Field(..., description="目标平均镜头时长(秒)")
    target_variance: float = Field(..., description="目标时长方差")

    adjustments: List[RhythmAdjustment] = Field(..., description="具体的节奏调整方案")

    optimization_summary: str = Field(..., description="优化方案的总体说明")
    expected_improvement: str = Field(..., description="预期的改进效果")


# --- 时长压缩模型 ---
class BeatPriority(BaseModel):
    """节拍优先级评估"""

    beat_index: int = Field(..., description="节拍索引")
    priority_score: float = Field(..., ge=0.0, le=1.0, description="优先级分数 (0-1)")
    content_type: str = Field(..., description="内容类型 (核心情节/过渡/细节描述)")
    keep_decision: str = Field(..., description="保留决定 (保留/压缩/删除)")
    justification: str = Field(..., description="决定理由")


class DurationCompressionResponse(BaseModel):
    """时长压缩响应"""

    original_duration: float = Field(..., description="原始总时长(秒)")
    target_duration: float = Field(..., description="目标时长(秒)")
    final_duration: float = Field(..., description="压缩后时长(秒)")
    compression_ratio: float = Field(..., description="实际压缩比例")

    beat_priorities: List[BeatPriority] = Field(..., description="各节拍的优先级评估")

    compression_strategy: str = Field(..., description="采用的压缩策略描述")
    quality_preservation_notes: str = Field(..., description="质量保持说明")

    removed_beats_count: int = Field(..., description="删除的节拍数量")
    compressed_beats_count: int = Field(..., description="压缩的节拍数量")


# --- 剪辑审查模型 ---
class EvaluationReview(BaseModel):
    """对评估结果的审查"""

    dimension_name: str = Field(..., description="被审查的维度名称")
    original_score: float = Field(..., description="原始评分")
    is_reasonable: bool = Field(..., description="评分是否合理")
    suggested_score: Optional[float] = Field(None, description="建议的修正评分")
    review_reason: str = Field(..., description="审查意见")


class EditingReviewResponse(BaseModel):
    """剪辑审查响应"""

    overall_evaluation_is_reasonable: bool = Field(..., description="整体评估是否合理")

    dimension_reviews: List[EvaluationReview] = Field(..., description="各维度评估的审查结果")

    critical_issues_review: str = Field(..., description="对致命问题分类的审查意见")
    suggestions_review: str = Field(..., description="对优化建议的审查意见")

    evaluation_consistency_score: float = Field(..., ge=0.0, le=5.0, description="评估一致性得分")

    final_recommendation: str = Field(..., description="最终建议 (接受评估/重新评估/人工审核)")
    review_summary: str = Field(..., description="审查总结")


# --- 智能镜头选择模型 ---
class ShotSelectionCandidate(BaseModel):
    """候选镜头评估"""

    shot_order_id: int = Field(..., description="镜头顺序ID")
    content_match_score: float = Field(..., ge=0.0, le=1.0, description="内容匹配分数")
    emotion_match_score: float = Field(..., ge=0.0, le=1.0, description="情感匹配分数")
    duration_match_score: float = Field(..., ge=0.0, le=1.0, description="时长匹配分数")
    overall_score: float = Field(..., ge=0.0, le=1.0, description="综合评分")
    selection_reason: str = Field(..., description="选择或排除的理由")


class IntelligentShotSelectionResponse(BaseModel):
    """智能镜头选择响应"""

    selected_shot_order_ids: List[int] = Field(..., description="最终选择的镜头ID列表")

    candidate_evaluations: List[ShotSelectionCandidate] = Field(..., description="所有候选镜头的评估结果")

    total_selected_duration: float = Field(..., description="选择镜头的总时长(秒)")
    target_duration: float = Field(..., description="目标时长(秒)")
    duration_match_ratio: float = Field(..., description="时长匹配比例")

    selection_strategy: str = Field(..., description="选择策略说明")
    overall_confidence: float = Field(..., ge=0.0, le=1.0, description="整体置信度")

    fallback_applied: bool = Field(default=False, description="是否应用了降级备选方案")
    fallback_reason: Optional[str] = Field(None, description="降级原因")

    justification: str = Field(..., description="最终选择的综合理由")
